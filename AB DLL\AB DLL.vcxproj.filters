﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Detours">
      <UniqueIdentifier>{50ec3e77-cde2-4753-885b-f822ba4c305d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Detours\detours.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="Detours\detver.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="Detours\enigma_ide.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="framework.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="MemoryReader.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="Detours\syelog.h">
      <Filter>Detours</Filter>
    </ClInclude>
    <ClInclude Include="Map2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\curl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\curlver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\easy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\mprintf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\multi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\stdcheaders.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\typecheck-gcc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\curl\urlapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\conversions\from_json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\conversions\to_chars.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\conversions\to_json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\binary_reader.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\input_adapters.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\json_sax.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\lexer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\parser.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\input\position_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\internal_iterator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\iter_impl.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\iteration_proxy.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\iterator_traits.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\json_reverse_iterator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\iterators\primitive_iterator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\meta\cpp_future.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\meta\detected.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\meta\is_sax.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\meta\type_traits.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\meta\void_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\output\binary_writer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\output\output_adapters.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\output\serializer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\exceptions.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\hash.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\json_pointer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\json_ref.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\macro_scope.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\macro_unscope.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\detail\value_t.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\thirdparty\hedley\hedley.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\thirdparty\hedley\hedley_undef.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\adl_serializer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\byte_container_with_subtype.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\json_fwd.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\nlohmann\ordered_map.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\auth.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\hmac_sha256.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\includes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\integrity.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\Security.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\sha256.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lib\xorstr.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="skStr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="discord_webhook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MemoryReader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lib\auth.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lib\hmac_sha256.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lib\sha256.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lib\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="AB DLL.def">
      <Filter>Source Files</Filter>
    </None>
    <None Include="Detours\detours.pdb" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="Detours\detours.lib" />
    <Library Include="Detours\enigma_ide.lib" />
    <Library Include="Detours\syelog.lib" />
  </ItemGroup>
</Project>
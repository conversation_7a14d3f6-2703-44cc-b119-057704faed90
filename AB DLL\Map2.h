// map.hpp - Single-file header implementation of std::map
// Based on original STL code from 2003, adapted for modern C++

#pragma once
#ifndef MAP_HPP
#define MAP_HPP

#define _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
#define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING

#include <functional>
#include <memory>
#include <stdexcept>
#include <utility>
#include <iterator>
#include <cstdlib>
#include <new>
#include <string>


namespace std2 {

    // Import necessary components from std::
    using std::pair;
    using std::iterator;
    using std::reverse_iterator;
    using std::iterator_traits;
    using std::bidirectional_iterator_tag;
    using std::ptrdiff_t;
    using std::size_t;
    using std::exception;
    using std::string;

    // Default comparator
    template<class _Ty>
    struct less {
        bool operator()(const _Ty& _Left, const _Ty& _Right) const {
            return _Left < _Right;
        }
    };

    // Exception classes
    class logic_error : public exception {
        string _Str;
    public:
        explicit logic_error(const string& _Message) : _Str(_Message) {}
        const char* what() const noexcept override { return _Str.c_str(); }
    };

    class out_of_range : public logic_error {
    public:
        explicit out_of_range(const string& _Message) : logic_error(_Message) {}
    };

    class length_error : public logic_error {
    public:
        explicit length_error(const string& _Message) : logic_error(_Message) {}
    };

    // Allocator base
    template<class _Ty>
    struct _Allocator_base {
        typedef _Ty value_type;
    };

    template<class _Ty>
    struct _Allocator_base<const _Ty> {
        typedef _Ty value_type;
    };

    // Default allocator
    template<class _Ty>
    class allocator : public _Allocator_base<_Ty> {
    public:
        typedef _Ty value_type;
        typedef value_type* pointer;
        typedef const value_type* const_pointer;
        typedef value_type& reference;
        typedef const value_type& const_reference;
        typedef size_t size_type;
        typedef ptrdiff_t difference_type;

        template<class _Other>
        struct rebind {
            typedef allocator<_Other> other;
        };

        pointer address(reference _Val) const { return &_Val; }
        const_pointer address(const_reference _Val) const { return &_Val; }

        allocator() {}
        allocator(const allocator<_Ty>&) {}
        template<class _Other>
        allocator(const allocator<_Other>&) {}
        template<class _Other>
        allocator<_Ty>& operator=(const allocator<_Other>&) { return *this; }

        void deallocate(pointer _Ptr, size_type) { ::operator delete(_Ptr); }
        pointer allocate(size_type _Count) {
            return static_cast<pointer>(::operator new(_Count * sizeof(_Ty)));
        }
        pointer allocate(size_type _Count, const void*) { return allocate(_Count); }

        void construct(pointer _Ptr, const _Ty& _Val) { new (_Ptr) _Ty(_Val); }
        void destroy(pointer _Ptr) { _Ptr->~_Ty(); }

        size_type max_size() const {
            return (static_cast<size_type>(-1) / sizeof(_Ty));
        }
    };

    template<class _Ty, class _Other>
    bool operator==(const allocator<_Ty>&, const allocator<_Other>&) { return true; }

    template<class _Ty, class _Other>
    bool operator!=(const allocator<_Ty>&, const allocator<_Other>&) { return false; }

    // Specialization for void
    template<>
    class allocator<void> {
    public:
        typedef void value_type;
        typedef void* pointer;
        typedef const void* const_pointer;

        template<class _Other>
        struct rebind {
            typedef allocator<_Other> other;
        };
    };

    // Tree implementation
    enum _Redbl { _Red, _Black };

    template<class _Traits>
    class _Tree {
    protected:
        struct _Node;
        typedef typename _Traits::allocator_type allocator_type;
        typedef typename allocator_type::template rebind<_Node>::other::pointer _Nodeptr;
        typedef typename allocator_type::template rebind<_Nodeptr>::other _Nodeptr_alloc;

        struct _Node {
            _Nodeptr _Left;
            _Nodeptr _Parent;
            _Nodeptr _Right;
            typename _Traits::value_type _Myval;
            char _Color;
            char _Isnil;

            _Node(_Nodeptr _Larg, _Nodeptr _Parg, _Nodeptr _Rarg,
                const typename _Traits::value_type& _Val, char _Carg)
                : _Left(_Larg), _Parent(_Parg), _Right(_Rarg),
                _Myval(_Val), _Color(_Carg), _Isnil(false) {
            }
        };

        allocator_type _Alval;
        typename allocator_type::template rebind<_Node>::other _Alnod;
        _Nodeptr_alloc _Alptr;
        _Nodeptr _Myhead;
        size_t _Mysize;

        _Nodeptr _Buynode(_Nodeptr _Larg, _Nodeptr _Parg, _Nodeptr _Rarg,
            const typename _Traits::value_type& _Val, char _Carg) {
            _Nodeptr _Wherenode = _Alnod.allocate(1);
            try {
                new (_Wherenode) _Node(_Larg, _Parg, _Rarg, _Val, _Carg);
            }
            catch (...) {
                _Alnod.deallocate(_Wherenode, 1);
                throw;
            }
            return _Wherenode;
        }

        _Nodeptr _Buynode() {
            _Nodeptr _Wherenode = _Alnod.allocate(1);
            _Alptr.construct(&_Left(_Wherenode), _Nodeptr());
            _Alptr.construct(&_Parent(_Wherenode), _Nodeptr());
            _Alptr.construct(&_Right(_Wherenode), _Nodeptr());
            _Color(_Wherenode) = _Black;
            _Isnil(_Wherenode) = false;
            return _Wherenode;
        }

        static _Nodeptr& _Left(_Nodeptr _Pnode) { return (_Pnode->_Left); }
        static _Nodeptr& _Right(_Nodeptr _Pnode) { return (_Pnode->_Right); }
        static _Nodeptr& _Parent(_Nodeptr _Pnode) { return (_Pnode->_Parent); }
        static char& _Color(_Nodeptr _Pnode) { return (_Pnode->_Color); }
        static char& _Isnil(_Nodeptr _Pnode) { return (_Pnode->_Isnil); }
        static typename _Traits::value_type& _Myval(_Nodeptr _Pnode) { return (_Pnode->_Myval); }

        _Nodeptr& _Root() const { return _Parent(_Myhead); }
        _Nodeptr& _Lmost() const { return _Left(_Myhead); }
        _Nodeptr& _Rmost() const { return _Right(_Myhead); }

        void _Init() {
            _Myhead = _Buynode();
            _Isnil(_Myhead) = true;
            _Root() = _Myhead;
            _Lmost() = _Myhead;
            _Rmost() = _Myhead;
            _Mysize = 0;
        }

        void _Tidy() {
            erase(begin(), end());
            _Alptr.destroy(&_Left(_Myhead));
            _Alptr.destroy(&_Parent(_Myhead));
            _Alptr.destroy(&_Right(_Myhead));
            _Alnod.deallocate(_Myhead, 1);
            _Myhead = nullptr;
            _Mysize = 0;
        }

        static _Nodeptr _Min(_Nodeptr _Pnode) {
            while (!_Isnil(_Left(_Pnode)))
                _Pnode = _Left(_Pnode);
            return _Pnode;
        }

        static _Nodeptr _Max(_Nodeptr _Pnode) {
            while (!_Isnil(_Right(_Pnode)))
                _Pnode = _Right(_Pnode);
            return _Pnode;
        }

    public:
        typedef _Tree<_Traits> _Myt;
        typedef typename _Traits::key_type key_type;
        typedef typename _Traits::value_type value_type;
        typedef typename _Traits::key_compare key_compare;
        typedef typename _Traits::value_compare value_compare;
        typedef typename allocator_type::pointer pointer;
        typedef typename allocator_type::const_pointer const_pointer;
        typedef typename allocator_type::reference reference;
        typedef typename allocator_type::const_reference const_reference;
        typedef typename allocator_type::size_type size_type;
        typedef typename allocator_type::difference_type difference_type;

        // Iterator classes must be defined before reverse_iterator typedefs
        class const_iterator : public std::iterator<bidirectional_iterator_tag, value_type, difference_type, const_pointer, const_reference> {
        protected:
            _Nodeptr _Ptr;

            void _Dec() {
                if (_Isnil(_Ptr))
                    _Ptr = _Right(_Ptr);
                else if (!_Isnil(_Left(_Ptr)))
                    _Ptr = _Max(_Left(_Ptr));
                else {
                    _Nodeptr _Pnode;
                    while (!_Isnil(_Pnode = _Parent(_Ptr)) && _Ptr == _Left(_Pnode))
                        _Ptr = _Pnode;
                    if (!_Isnil(_Pnode))
                        _Ptr = _Pnode;
                }
            }

            void _Inc() {
                if (_Isnil(_Ptr))
                    return;
                else if (!_Isnil(_Right(_Ptr)))
                    _Ptr = _Min(_Right(_Ptr));
                else {
                    _Nodeptr _Pnode;
                    while (!_Isnil(_Pnode = _Parent(_Ptr)) && _Ptr == _Right(_Pnode))
                        _Ptr = _Pnode;
                    _Ptr = _Pnode;
                }
            }

        public:
            const_iterator() : _Ptr(nullptr) {}
            const_iterator(_Nodeptr _Pnode) : _Ptr(_Pnode) {}
            const_reference operator*() const { return _Myval(_Ptr); }
            const_pointer operator->() const { return &**this; }
            const_iterator& operator++() { _Inc(); return *this; }
            const_iterator operator++(int) { const_iterator _Tmp = *this; ++*this; return _Tmp; }
            const_iterator& operator--() { _Dec(); return *this; }
            const_iterator operator--(int) { const_iterator _Tmp = *this; --*this; return _Tmp; }
            bool operator==(const const_iterator& _Right) const { return _Ptr == _Right._Ptr; }
            bool operator!=(const const_iterator& _Right) const { return !(*this == _Right); }

            _Nodeptr _Mynode() const { return _Ptr; }
        };

        class iterator : public const_iterator {
        public:
            iterator() : const_iterator() {}
            iterator(_Nodeptr _Pnode) : const_iterator(_Pnode) {}
            reference operator*() const { return _Myval(this->_Ptr); }
            pointer operator->() const { return &**this; }
            iterator& operator++() { this->_Inc(); return *this; }
            iterator operator++(int) { iterator _Tmp = *this; ++*this; return _Tmp; }
            iterator& operator--() { this->_Dec(); return *this; }
            iterator operator--(int) { iterator _Tmp = *this; --*this; return _Tmp; }
        };

        // Now we can define the reverse_iterator types
        typedef reverse_iterator<iterator> reverse_iterator;
        typedef std::reverse_iterator<const_iterator> const_reverse_iterator;

        explicit _Tree(const key_compare& _Parg, const allocator_type& _Al)
            : _Alval(_Al), _Alnod(_Al), _Alptr(_Al), _Mysize(0) {
            _Init();
            this->comp = _Parg;
        }

        ~_Tree() { _Tidy(); }

        iterator begin() { return iterator(_Lmost()); }
        const_iterator begin() const { return const_iterator(_Lmost()); }
        iterator end() { return iterator(_Myhead); }
        const_iterator end() const { return const_iterator(_Myhead); }
        reverse_iterator rbegin() { return reverse_iterator(end()); }
        const_reverse_iterator rbegin() const { return const_reverse_iterator(end()); }
        reverse_iterator rend() { return reverse_iterator(begin()); }
        const_reverse_iterator rend() const { return const_reverse_iterator(begin()); }

        size_type size() const { return _Mysize; }
        size_type max_size() const { return _Alval.max_size(); }
        bool empty() const { return size() == 0; }
        allocator_type get_allocator() const { return _Alval; }
        key_compare key_comp() const { return this->comp; }
        value_compare value_comp() const { return value_compare(key_comp()); }

    protected:
        key_compare comp;
    };

    // Map traits
    template<class _Kty, class _Ty, class _Pr, class _Alloc, bool _Mfl>
    class _Tmap_traits {
    public:
        typedef _Kty key_type;
        typedef pair<const _Kty, _Ty> value_type;
        typedef _Pr key_compare;
        typedef typename _Alloc::template rebind<value_type>::other allocator_type;
        typedef value_type* _ITptr;
        typedef value_type& _IReft;
        enum { _Multi = _Mfl };

        _Tmap_traits() : comp() {}
        _Tmap_traits(_Pr _Parg) : comp(_Parg) {}

        class value_compare {
        public:
            typedef bool result_type;
            typedef value_type first_argument_type;
            typedef value_type second_argument_type;

            bool operator()(const value_type& _Left, const value_type& _Right) const {
                return comp(_Left.first, _Right.first);
            }
            value_compare(key_compare _Pred) : comp(_Pred) {}
        protected:
            key_compare comp;
        };

        static const _Kty& _Kfn(const value_type& _Val) { return _Val.first; }
        _Pr comp;
    };

    // Map class
    template<class _Kty, class _Ty, class _Pr = less<_Kty>, class _Alloc = allocator<pair<const _Kty, _Ty>>>
    class map : public _Tree<_Tmap_traits<_Kty, _Ty, _Pr, _Alloc, false>> {
    public:
        typedef map<_Kty, _Ty, _Pr, _Alloc> _Myt;
        typedef _Tree<_Tmap_traits<_Kty, _Ty, _Pr, _Alloc, false>> _Mybase;
        typedef _Kty key_type;
        typedef _Ty mapped_type;
        typedef _Pr key_compare;
        typedef typename _Mybase::value_compare value_compare;
        typedef typename _Mybase::allocator_type allocator_type;
        typedef typename _Mybase::size_type size_type;
        typedef typename _Mybase::difference_type difference_type;
        typedef typename _Mybase::pointer pointer;
        typedef typename _Mybase::const_pointer const_pointer;
        typedef typename _Mybase::reference reference;
        typedef typename _Mybase::const_reference const_reference;
        typedef typename _Mybase::iterator iterator;
        typedef typename _Mybase::const_iterator const_iterator;
        typedef typename _Mybase::reverse_iterator reverse_iterator;
        typedef typename _Mybase::const_reverse_iterator const_reverse_iterator;
        typedef typename _Mybase::value_type value_type;

        map() : _Mybase(key_compare(), allocator_type()) {}
        explicit map(const key_compare& _Pred) : _Mybase(_Pred, allocator_type()) {}
        map(const key_compare& _Pred, const allocator_type& _Al) : _Mybase(_Pred, _Al) {}

        template<class _Iter>
        map(_Iter _First, _Iter _Last) : _Mybase(key_compare(), allocator_type()) {
            insert(_First, _Last);
        }

        template<class _Iter>
        map(_Iter _First, _Iter _Last, const key_compare& _Pred)
            : _Mybase(_Pred, allocator_type()) {
            insert(_First, _Last);
        }

        template<class _Iter>
        map(_Iter _First, _Iter _Last, const key_compare& _Pred, const allocator_type& _Al)
            : _Mybase(_Pred, _Al) {
            insert(_First, _Last);
        }

        mapped_type& operator[](const key_type& _Keyval) {
            iterator _Where = this->lower_bound(_Keyval);
            if (_Where == this->end() || this->comp(_Keyval, _Where->first))
                _Where = this->insert(_Where, value_type(_Keyval, mapped_type()));
            return _Where->second;
        }
    };

    template<class _Kty, class _Ty, class _Pr, class _Alloc>
    void swap(map<_Kty, _Ty, _Pr, _Alloc>& _Left, map<_Kty, _Ty, _Pr, _Alloc>& _Right) {
        _Left.swap(_Right);
    }

    // Multimap class
    template<class _Kty, class _Ty, class _Pr = less<_Kty>, class _Alloc = allocator<pair<const _Kty, _Ty>>>
    class multimap : public _Tree<_Tmap_traits<_Kty, _Ty, _Pr, _Alloc, true>> {
    public:
        typedef multimap<_Kty, _Ty, _Pr, _Alloc> _Myt;
        typedef _Tree<_Tmap_traits<_Kty, _Ty, _Pr, _Alloc, true>> _Mybase;
        typedef _Kty key_type;
        typedef _Ty mapped_type;
        typedef _Pr key_compare;
        typedef typename _Mybase::value_compare value_compare;
        typedef typename _Mybase::allocator_type allocator_type;
        typedef typename _Mybase::size_type size_type;
        typedef typename _Mybase::difference_type difference_type;
        typedef typename _Mybase::pointer pointer;
        typedef typename _Mybase::const_pointer const_pointer;
        typedef typename _Mybase::reference reference;
        typedef typename _Mybase::const_reference const_reference;
        typedef typename _Mybase::iterator iterator;
        typedef typename _Mybase::const_iterator const_iterator;
        typedef typename _Mybase::reverse_iterator reverse_iterator;
        typedef typename _Mybase::const_reverse_iterator const_reverse_iterator;
        typedef typename _Mybase::value_type value_type;

        multimap() : _Mybase(key_compare(), allocator_type()) {}
        explicit multimap(const key_compare& _Pred) : _Mybase(_Pred, allocator_type()) {}
        multimap(const key_compare& _Pred, const allocator_type& _Al) : _Mybase(_Pred, _Al) {}

        template<class _Iter>
        multimap(_Iter _First, _Iter _Last) : _Mybase(key_compare(), allocator_type()) {
            insert(_First, _Last);
        }

        template<class _Iter>
        multimap(_Iter _First, _Iter _Last, const key_compare& _Pred)
            : _Mybase(_Pred, allocator_type()) {
            insert(_First, _Last);
        }

        template<class _Iter>
        multimap(_Iter _First, _Iter _Last, const key_compare& _Pred, const allocator_type& _Al)
            : _Mybase(_Pred, _Al) {
            insert(_First, _Last);
        }
    };

    template<class _Kty, class _Ty, class _Pr, class _Alloc>
    void swap(multimap<_Kty, _Ty, _Pr, _Alloc>& _Left, multimap<_Kty, _Ty, _Pr, _Alloc>& _Right) {
        _Left.swap(_Right);
    }

} // namespace std2

#endif // MAP_HPP
﻿  MemoryReader.cpp
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(146,65): warning C4312: 'type cast': conversion from 'DWORD' to 'void *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(298,36): warning C4312: 'type cast': conversion from 'DWORD' to 'DWORD *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(333,75): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(333,75): warning C4267:         with
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(333,75): warning C4267:         [
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(333,75): warning C4267:             _Ty=int
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(333,75): warning C4267:         ]
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(375,10): warning C4312: 'type cast': conversion from 'const DWORD' to 'int *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,26): warning C4311: 'type cast': pointer truncation from 'void *const ' to 'DWORD'
  (compiling source file '/MemoryReader.cpp')
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,26):
      the template instantiation context (the oldest one first) is
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(91,34):
          see reference to function template instantiation 'T TargetInfo::ReadMemory<int>(DWORD,T) const' being compiled
          with
          [
              T=int
          ]
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,26): warning C4302: 'type cast': truncation from 'void *const ' to 'DWORD'
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,21): warning C4312: 'type cast': conversion from 'unsigned long' to 'int *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,21): warning C4312: 'type cast': conversion from 'unsigned long' to 'DWORD *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(83,21): warning C4312: 'type cast': conversion from 'unsigned long' to 'BYTE *' of greater size
  (compiling source file '/MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(134,21): warning C4312: 'type cast': conversion from 'DWORD' to 'const char **' of greater size
  (compiling source file '/MemoryReader.cpp')
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(134,21):
      the template instantiation context (the oldest one first) is
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(157,31):
          see reference to function template instantiation 'T *PlayerInfo::ReadMemoryAbsolute<const char*>(DWORD,T) const' being compiled
          with
          [
              T=const char *
          ]
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(134,21): warning C4312: 'type cast': conversion from 'DWORD' to 'int *' of greater size
  (compiling source file '/MemoryReader.cpp')
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(134,21):
      the template instantiation context (the oldest one first) is
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(162,23):
          see reference to function template instantiation 'T PlayerInfo::ReadMemoryAbsolute<int>(DWORD,T) const' being compiled
          with
          [
              T=int
          ]
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(148,21): warning C4312: 'type cast': conversion from 'unsigned long' to 'int *' of greater size
  (compiling source file '/MemoryReader.cpp')
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(148,21):
      the template instantiation context (the oldest one first) is
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.h(172,34):
          see reference to function template instantiation 'T PlayerInfo::ReadMemoryOffset<int>(DWORD,T) const' being compiled
          with
          [
              T=int
          ]
  
     Creating library G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\x64\Release\AB DLL.lib and object G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\x64\Release\AB DLL.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 649 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  AB DLL.vcxproj -> G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\x64\Release\AB DLL.dll

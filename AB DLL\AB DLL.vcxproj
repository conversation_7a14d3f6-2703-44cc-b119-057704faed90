<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{1cb78ea9-9bdd-4fa1-97da-d6e5685a4811}</ProjectGuid>
    <RootNamespace>ABDLL</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LibraryPath>./lib/curl;.\;$(LibraryPath)</LibraryPath>
    <IncludePath>./lib/nlohmann;./lib/curl;./lib;.\;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LibraryPath>G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Detours;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;ABDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>detours.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>AB DLL.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>false</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;ABDLL_EXPORTS;_WINDOWS;_USRDLL;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <UndefinePreprocessorDefinitions>/D _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS;/D _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING</UndefinePreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>AB DLL.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;ABDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>detours.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>AB DLL.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;ABDLL_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <AdditionalDependencies>detours.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ModuleDefinitionFile>AB DLL.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Detours\detours.h" />
    <ClInclude Include="Detours\detver.h" />
    <ClInclude Include="Detours\enigma_ide.h" />
    <ClInclude Include="Detours\syelog.h" />
    <ClInclude Include="discord_webhook.h" />
    <ClInclude Include="framework.h" />
    <ClInclude Include="json.hpp" />
    <ClInclude Include="lib\auth.hpp" />
    <ClInclude Include="lib\curl\curl.h" />
    <ClInclude Include="lib\curl\curlver.h" />
    <ClInclude Include="lib\curl\easy.h" />
    <ClInclude Include="lib\curl\mprintf.h" />
    <ClInclude Include="lib\curl\multi.h" />
    <ClInclude Include="lib\curl\stdcheaders.h" />
    <ClInclude Include="lib\curl\system.h" />
    <ClInclude Include="lib\curl\typecheck-gcc.h" />
    <ClInclude Include="lib\curl\urlapi.h" />
    <ClInclude Include="lib\hmac_sha256.h" />
    <ClInclude Include="lib\includes.hpp" />
    <ClInclude Include="lib\integrity.h" />
    <ClInclude Include="lib\nlohmann\adl_serializer.hpp" />
    <ClInclude Include="lib\nlohmann\byte_container_with_subtype.hpp" />
    <ClInclude Include="lib\nlohmann\detail\conversions\from_json.hpp" />
    <ClInclude Include="lib\nlohmann\detail\conversions\to_chars.hpp" />
    <ClInclude Include="lib\nlohmann\detail\conversions\to_json.hpp" />
    <ClInclude Include="lib\nlohmann\detail\exceptions.hpp" />
    <ClInclude Include="lib\nlohmann\detail\hash.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\binary_reader.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\input_adapters.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\json_sax.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\lexer.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\parser.hpp" />
    <ClInclude Include="lib\nlohmann\detail\input\position_t.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\internal_iterator.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\iteration_proxy.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\iterator_traits.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\iter_impl.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\json_reverse_iterator.hpp" />
    <ClInclude Include="lib\nlohmann\detail\iterators\primitive_iterator.hpp" />
    <ClInclude Include="lib\nlohmann\detail\json_pointer.hpp" />
    <ClInclude Include="lib\nlohmann\detail\json_ref.hpp" />
    <ClInclude Include="lib\nlohmann\detail\macro_scope.hpp" />
    <ClInclude Include="lib\nlohmann\detail\macro_unscope.hpp" />
    <ClInclude Include="lib\nlohmann\detail\meta\cpp_future.hpp" />
    <ClInclude Include="lib\nlohmann\detail\meta\detected.hpp" />
    <ClInclude Include="lib\nlohmann\detail\meta\is_sax.hpp" />
    <ClInclude Include="lib\nlohmann\detail\meta\type_traits.hpp" />
    <ClInclude Include="lib\nlohmann\detail\meta\void_t.hpp" />
    <ClInclude Include="lib\nlohmann\detail\output\binary_writer.hpp" />
    <ClInclude Include="lib\nlohmann\detail\output\output_adapters.hpp" />
    <ClInclude Include="lib\nlohmann\detail\output\serializer.hpp" />
    <ClInclude Include="lib\nlohmann\detail\value_t.hpp" />
    <ClInclude Include="lib\nlohmann\json.hpp" />
    <ClInclude Include="lib\nlohmann\json_fwd.hpp" />
    <ClInclude Include="lib\nlohmann\ordered_map.hpp" />
    <ClInclude Include="lib\nlohmann\thirdparty\hedley\hedley.hpp" />
    <ClInclude Include="lib\nlohmann\thirdparty\hedley\hedley_undef.hpp" />
    <ClInclude Include="lib\Security.hpp" />
    <ClInclude Include="lib\sha256.h" />
    <ClInclude Include="lib\utils.hpp" />
    <ClInclude Include="lib\xorstr.hpp" />
    <ClInclude Include="Map2.h" />
    <ClInclude Include="MemoryReader.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="skStr.h" />
    <ClInclude Include="utils.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp" />
    <ClCompile Include="lib\auth.cpp" />
    <ClCompile Include="lib\hmac_sha256.c" />
    <ClCompile Include="lib\sha256.c" />
    <ClCompile Include="lib\utils.cpp" />
    <ClCompile Include="MemoryReader.cpp" />
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="AB DLL.def" />
    <None Include="Detours\detours.pdb" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="Detours\detours.lib" />
    <Library Include="Detours\enigma_ide.lib" />
    <Library Include="Detours\syelog.lib" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
﻿  MemoryReader.cpp
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(25,16): warning C4996: 'std::iterator': warning STL4015: The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17. (The <iterator> header is NOT deprecated.) The C++ Standard has never required user-defined iterators to derive from std::iterator. To fix this warning, stop deriving from std::iterator and start providing publicly accessible typedefs named iterator_category, value_type, difference_type, pointer, and reference. Note that value_type is required to be non-const, even for constant iterators. You can define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
  (compiling source file 'MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\lib\auth.hpp(110,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'MemoryReader.cpp')
  
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996: 'std::iterator<std::bidirectional_iterator_tag,std::pair<const _Kty,_Ty>,int,const std::pair<const _Kty,_Ty> *,const std::pair<const _Kty,_Ty> &>': warning STL4015: The std::iterator class template (used as a base class to provide typedefs) is deprecated in C++17. (The <iterator> header is NOT deprecated.) The C++ Standard has never required user-defined iterators to derive from std::iterator. To fix this warning, stop deriving from std::iterator and start providing publicly accessible typedefs named iterator_category, value_type, difference_type, pointer, and reference. Note that value_type is required to be non-const, even for constant iterators. You can define _SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996:         with
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996:         [
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996:             _Kty=int,
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996:             _Ty=TargetInfo
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44): warning C4996:         ]
  (compiling source file 'MemoryReader.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(7545,45):
      see declaration of 'std::iterator'
      G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,44):
      the template instantiation context (the oldest one first) is
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(289,15):
          while compiling class 'std2::_Tree<std2::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::iterator'
          with
          [
              _Kty=int,
              _Ty=TargetInfo,
              _Pr=std2::less<int>,
              _Alloc=std2::allocator<std::pair<const int,TargetInfo>>
          ]
          G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\Map2.h(243,15):
          while compiling class 'std2::_Tree<std2::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::const_iterator'
          with
          [
              _Kty=int,
              _Ty=TargetInfo,
              _Pr=std2::less<int>,
              _Alloc=std2::allocator<std::pair<const int,TargetInfo>>
          ]
  
     Creating library G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.lib and object G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.exp
  Generating code
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\MemoryReader.cpp(152): warning C4715: 'LoadKeyAuth': not all control paths return a value
G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\AB DLL\discord_webhook.h(151): warning C4715: 'SendDiscordLoginEmbedAsync': not all control paths return a value
  126 of 2681 functions ( 4.7%) were compiled, the rest were copied from previous compilation.
    54 functions were new in current compilation
    102 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  AB DLL.vcxproj -> G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.dll

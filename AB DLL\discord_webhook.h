#include <windows.h>
#include <wininet.h>
#include <string>
#include <thread>
#include <sstream>
#include <iomanip>
#pragma comment(lib, "wininet.lib")



// Escape strings for safe JSON
std::string JsonEscape(const std::string& input) {
    std::string out;
    out.reserve(input.size());
    for (char c : input) {
        switch (c) {
        case '\"': out += "\\\""; break;
        case '\\': out += "\\\\"; break;
        case '\n': out += "\\n"; break;
        case '\r': out += "\\r"; break;
        case '\t': out += "\\t"; break;
        default: out += c; break;
        }
    }
    return out;
}

// Get ISO8601 timestamp
std::string GetIsoTimestamp() {
    SYSTEMTIME st;
    GetSystemTime(&st);
    std::ostringstream oss;
    oss << std::setfill('0')
        << st.wYear << "-"
        << std::setw(2) << st.wMonth << "-"
        << std::setw(2) << st.wDay << "T"
        << std::setw(2) << st.wHour << ":"
        << std::setw(2) << st.wMinute << ":"
        << std::setw(2) << st.wSecond << "Z";
    return oss.str();
}

static bool SendDiscordEmbedBlocking(const std::string& webhookUrl, const std::string& jsonPayload) {
    HINTERNET hInternet = InternetOpenA("DiscordBot", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return false;

    URL_COMPONENTSA urlComp{};
    char hostName[256], urlPath[1024];
    urlComp.dwStructSize = sizeof(urlComp);
    urlComp.lpszHostName = hostName;
    urlComp.dwHostNameLength = sizeof(hostName);
    urlComp.lpszUrlPath = urlPath;
    urlComp.dwUrlPathLength = sizeof(urlPath);

    if (!InternetCrackUrlA(webhookUrl.c_str(), 0, 0, &urlComp)) {
        InternetCloseHandle(hInternet);
        return false;
    }

    HINTERNET hConnect = InternetConnectA(
        hInternet, urlComp.lpszHostName, urlComp.nPort, NULL, NULL, INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return false;
    }

    HINTERNET hRequest = HttpOpenRequestA(
        hConnect, "POST", urlComp.lpszUrlPath, NULL, NULL, NULL,
        INTERNET_FLAG_SECURE | INTERNET_FLAG_NO_CACHE_WRITE, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return false;
    }

    std::string headers = "Content-Type: application/json\r\n";

    BOOL result = HttpSendRequestA(
        hRequest,
        headers.c_str(),
        (DWORD)headers.length(),
        (LPVOID)jsonPayload.c_str(),
        (DWORD)jsonPayload.length());

    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);

    return result == TRUE;
}


inline void SendDiscordLoginAttemptEmbedAsync(
    const std::string& webhookUrl,
    const std::string& username,
    const std::string& hwid,
    const std::string& playerName
) {
    std::thread([=]() {
        std::string json =
            "{"
            "\"embeds\": [{"
            "\"title\": \"⚠️ Login Attempt\","
            "\"description\": \"A user attempted to log in.\","
            "\"color\": 16776960," // yellow
            "\"fields\": ["
            "{ \"name\": \"🔑 Username\", \"value\": \"" + JsonEscape(username) + "\", \"inline\": true },"
            "{ \"name\": \"💻 HWID\", \"value\": \"" + JsonEscape(hwid) + "\", \"inline\": true },"
            "{ \"name\": \"🎮 IP\", \"value\": \"" + JsonEscape(playerName) + "\", \"inline\": false }"
            "],"
            "\"footer\": {"
            "\"text\": \"Auth System\""
            "},"
            "\"timestamp\": \"" + GetIsoTimestamp() + "\""
            "}]"
            "}";

        SendDiscordEmbedBlocking(webhookUrl, json);
        }).detach();
}



inline bool SendDiscordLoginEmbedAsync(
    const std::string& webhookUrl,
    const std::string& username,
    const std::string& hwid,
    const std::string& playerName
) {
    std::thread([=]() {
        std::string json =
            "{"
            "\"embeds\": [{"
            "\"title\": \"✅ Login Successful\","
            "\"description\": \"User has logged in.\","
            "\"color\": 3066993,"
            "\"fields\": ["
            "{ \"name\": \"🔑 Key\", \"value\": \"" + JsonEscape(username) + "\", \"inline\": true },"
            "{ \"name\": \"💻 HWID\", \"value\": \"" + JsonEscape(hwid) + "\", \"inline\": true },"
            "{ \"name\": \"🎮 IP\", \"value\": \"" + JsonEscape(playerName) + "\", \"inline\": false }"
            "],"
            "\"footer\": {"
            "\"text\": \"Auth System\""
            "},"
            "\"timestamp\": \"" + GetIsoTimestamp() + "\""
            "}]"
            "}";

        SendDiscordEmbedBlocking(webhookUrl, json);
        }).detach();
}
